# 🚀 COMPLETE RESTART GUIDE

## Step 1: Stop All Processes
```bash
# Windows
taskkill /f /im node.exe
taskkill /f /im shopify.exe

# Mac/Linux  
pkill -f "npm run dev"
pkill -f "shopify app dev"
```

## Step 2: Clear Cache and Restart
```bash
# Navigate to your project


# Clear npm cache
npm cache clean --force

# Restart development server
npm run dev
```

## Step 3: Get New Tunnel URL
Look for output like:
```
┌─ web ────────────────────────────────────────────────────────────────────────┐
│  Your app is running at: https://NEW-TUNNEL-URL.trycloudflare.com           │
└──────────────────────────────────────────────────────────────────────────────┘
```

## Step 4: Update Configuration
Update `shopify.app.toml` with the new URL:
```toml
application_url = "https://NEW-TUNNEL-URL.trycloudflare.com"
```

## Step 5: Test Connection
```bash
# Test the new URL
curl https://NEW-TUNNEL-URL.trycloudflare.com/api/health
```

## Step 6: Clear Browser Cache
1. Open Shopify Admin
2. Press Ctrl+Shift+R (hard refresh)
3. Clear browser cache completely

## Step 7: Test Extension
1. Go to Orders in Shopify Admin
2. Select an order
3. Click "Send to App"
4. Should work now!

## If Still Not Working:
1. Check the browser console for the actual tunnel URL being used
2. Make sure the development server is running
3. Try using ngrok instead of cloudflare tunnel
