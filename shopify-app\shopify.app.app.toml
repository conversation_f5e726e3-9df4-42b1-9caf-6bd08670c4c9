# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "09808303c37ea6e0e0aaeac9246884fd"
name = "APP"
handle = "app-1797"
application_url = "https://walter-rescue-occasion-plant.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = ["https://walter-rescue-occasion-plant.trycloudflare.com/auth/callback", "https://walter-rescue-occasion-plant.trycloudflare.com/auth/shopify/callback", "https://walter-rescue-occasion-plant.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
